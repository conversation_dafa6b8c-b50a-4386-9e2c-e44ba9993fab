#include "ad9959.h"
#include "gpio.h"

/* Modulation parameters storage */
AD9959_ModulationParams modulation_params;

/* Register Data Buffers */
uint8_t FR1_DATA[3] = {0xD0, 0x00, 0x00}; // 20x PLL
uint8_t FR2_DATA[2] = {0x00, 0x00};
uint8_t CFR_DATA[3] = {0x00, 0x03, 0x02};  
uint8_t CPOW0_DATA[2] = {0x00, 0x00};
uint8_t LSRR_DATA[2] = {0x00, 0x00};
uint8_t RDW_DATA[4] = {0x00, 0x00, 0x00, 0x00};
uint8_t FDW_DATA[4] = {0x00, 0x00, 0x00, 0x00};
uint8_t ACR_DATA[3] = {0x00, 0x10, 0x00};

/* Channel Select Data */
uint8_t CSR_DATA[4] = {0x10, 0x20, 0x40, 0x80}; // CH0-CH3

static void ad9959_delay(uint32_t length) {
    length = length * 12;
    while(length--);
}

/* Parameter setting functions */
/* Parameter setting functions */
void ad9959_set_channel_freq(AD9959_CHANNEL ch, uint32_t freq) {
    if(ch == AD9959_CHANNEL_0 || ch == AD9959_CHANNEL_1 || 
       ch == AD9959_CHANNEL_2 || ch == AD9959_CHANNEL_3) {
        ad9959_write_frequency(ch, freq);
    }
}

void ad9959_set_channel_amp(AD9959_CHANNEL ch, uint16_t amp) {
    if(ch == AD9959_CHANNEL_0 || ch == AD9959_CHANNEL_1 ||
       ch == AD9959_CHANNEL_2 || ch == AD9959_CHANNEL_3) {
        ad9959_write_amplitude(ch, amp);
    }
}

void ad9959_set_channel_phase(AD9959_CHANNEL ch, uint16_t phase) {
    if(ch == AD9959_CHANNEL_0 || ch == AD9959_CHANNEL_1 ||
       ch == AD9959_CHANNEL_2 || ch == AD9959_CHANNEL_3) {
        ad9959_write_phase(ch, phase);
    }
}

void ad9959_set_am_params(uint32_t freq, uint16_t high, uint16_t low, uint32_t step) {
    modulation_params.am.freq = freq;
    modulation_params.am.high = high;
    modulation_params.am.low = low;
    modulation_params.am.step = step;
}

void ad9959_set_ask_params(uint32_t freq, uint16_t high, uint16_t low) {
    modulation_params.ask.freq = freq;
    modulation_params.ask.high = high;
    modulation_params.ask.low = low;
}

void ad9959_init(void) {
    ad9959_io_init();
    ad9959_reset();

    ad9959_write_data(AD9959_REG_FR1, 3, FR1_DATA, true);
    ad9959_write_data(AD9959_REG_FR2, 2, FR2_DATA, true);
    
    // Default frequency for all channels (20MHz)
    for(uint8_t i=0; i<4; i++) {
        ad9959_write_frequency(i, 20000000);
        ad9959_write_phase(i, 0);
        ad9959_write_amplitude(i, 1023);
    }
}

void ad9959_reset(void) {
    AD9959_RESET_0;
    ad9959_delay(1);
    AD9959_RESET_1;
    ad9959_delay(30);
    AD9959_RESET_0;
}

void ad9959_io_init(void) {
    AD9959_PDC_0;
    AD9959_CS_1;
    AD9959_SCLK_0;
    AD9959_UPDATE_0;
    AD9959_SDIO0_0;
}

void ad9959_io_update(void) {
    AD9959_UPDATE_0;
    ad9959_delay(2);
    AD9959_UPDATE_1;
    ad9959_delay(4);
    AD9959_UPDATE_0;
}

void ad9959_write_data(AD9959_REG_ADDR reg, uint8_t len, const uint8_t *data, bool update) {
    AD9959_CS_0;
    
    // Write register address
    uint8_t addr = reg;
    for(uint8_t i=0; i<8; i++) {
        AD9959_SCLK_0;
            HAL_GPIO_WritePin(AD9959_SDIO0_GPIO_Port, AD9959_SDIO0_Pin, (addr & 0x80) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        AD9959_SCLK_1;
        addr <<= 1;
    }
    AD9959_SCLK_0;

    // Write data
    for(uint8_t i=0; i<len; i++) {
        uint8_t val = data[i];
        for(uint8_t j=0; j<8; j++) {
            AD9959_SCLK_0;
            HAL_GPIO_WritePin(AD9959_SDIO0_GPIO_Port, AD9959_SDIO0_Pin, (val & 0x80) ? GPIO_PIN_SET : GPIO_PIN_RESET);
            AD9959_SCLK_1;
            val <<= 1;
        }
        AD9959_SCLK_0;
    }

    if(update) ad9959_io_update();
    AD9959_CS_1;
}

void ad9959_write_frequency(AD9959_CHANNEL ch, uint32_t freq) {
    uint32_t temp = freq * 8.589934592; // 2^32/500MHz
    uint8_t data[4] = {
        (uint8_t)(temp >> 24),
        (uint8_t)(temp >> 16),
        (uint8_t)(temp >> 8),
        (uint8_t)temp
    };
    
    ad9959_write_data(AD9959_REG_CSR, 1, &CSR_DATA[ch>>4], true);
    ad9959_write_data(AD9959_REG_CFTW0, 4, data, true);
}

void ad9959_write_phase(AD9959_CHANNEL ch, uint16_t phase) {
    uint16_t temp = phase * 45.51111; // 2^14/360
    CPOW0_DATA[0] = (uint8_t)(temp >> 8);
    CPOW0_DATA[1] = (uint8_t)temp;
    
    ad9959_write_data(AD9959_REG_CSR, 1, &CSR_DATA[ch>>4], true);
    ad9959_write_data(AD9959_REG_CPOW0, 2, CPOW0_DATA, true);
}

void ad9959_write_amplitude(AD9959_CHANNEL ch, uint16_t amplitude) {
    amplitude |= 0x1000;
    ACR_DATA[1] = (uint8_t)(amplitude >> 8);
    ACR_DATA[2] = (uint8_t)amplitude;
    
    ad9959_write_data(AD9959_REG_CSR, 1, &CSR_DATA[ch>>4], true);
    ad9959_write_data(AD9959_REG_ACR, 3, ACR_DATA, true);
}

/* Enhanced Modulation Functions */
void ad9959_set_am_modulation(AD9959_CHANNEL ch, uint32_t base_freq, 
                             uint16_t amp1, uint16_t amp2, uint32_t step) {
    uint8_t cfr_am[3] = {0x40, 0x43, 0x20};
    uint8_t cw_data[4] = {0x00, 0x00, 0x00, 0x00};
    
    // Configure AM parameters
    uint16_t b_temp = (amp2 & 0x3FF) << 6;
    cw_data[0] = (uint8_t)(b_temp >> 8);
    cw_data[1] = (uint8_t)b_temp;
    
    ad9959_write_data(AD9959_REG_CSR, 1, &CSR_DATA[ch>>4], true);
    ad9959_write_data(AD9959_REG_CFR, 3, cfr_am, true);
    ad9959_write_amplitude(ch, amp1);
    ad9959_write_data(0x0A, 4, cw_data, true); // Write CW register
    ad9959_write_frequency(ch, base_freq);
}

void ad9959_set_ask_modulation(AD9959_CHANNEL ch, uint32_t freq, 
                              uint16_t amp1, uint16_t amp2) {
    uint8_t cfr_ask[3] = {0x40, 0x03, 0x30};
    uint8_t s2_data[4] = {0x1F, 0x30, 0x00, 0x00};
    
    // Configure ASK parameters
    uint16_t b_temp = amp2 & 0xFFC0;
    s2_data[0] = (uint8_t)(b_temp >> 8);
    s2_data[1] = (uint8_t)b_temp;
    
    ad9959_write_data(AD9959_REG_CSR, 1, &CSR_DATA[ch>>4], true);
    ad9959_write_data(AD9959_REG_CFR, 3, cfr_ask, true);
    ad9959_write_amplitude(ch, amp1);
    ad9959_write_data(0x0A, 4, s2_data, true); // Write S2 register
    ad9959_write_frequency(ch, freq);
}

void ad9959_sweep_frequency(AD9959_CHANNEL ch, uint32_t start, uint32_t stop, uint32_t step) {
    if(start < stop) {
        for(uint32_t f=start; f<=stop; f+=step) {
            ad9959_write_frequency(ch, f);
            HAL_Delay(1);
        }
    } else {
        for(uint32_t f=start; f>=stop; f-=step) {
            ad9959_write_frequency(ch, f);
            HAL_Delay(1);
        }
    }
}
