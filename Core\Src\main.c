/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

#include "math.h"
#include "arm_math.h"
#include "arm_const_structs.h"
#include "stdio.h"
#include "fft_processing.h"

#include "ad9959.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */


/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

//��ʼ����
uint16_t flag = 0;
#define ADC_REF_VOLTAGE 3.3f
#define ADC_MAX_VALUE 65535.0f


//fft��adcʹ��
#define FFT_LENGTH 1024
uint16_t adc_buff[FFT_LENGTH];//���ADC�ɼ�������
float buf_Value[FFT_LENGTH] = {0};//ADC������ѹ����
float testInput[FFT_LENGTH*2] = {0};//FFT��������
float testOutput[FFT_LENGTH] = {0};//FFT��������
#define max(a, b) ((a) > (b) ? (a) : (b))
#define min(a, b) ((a) < (b) ? (a) : (b))
float window[FFT_LENGTH]; // ������ϵ��
#define FREQ_HISTORY_SIZE 8
float freqHistory[FREQ_HISTORY_SIZE];
uint8_t freqHistoryIndex = 0; //Ƶ����ʷ����

// AD9959扫频相关变量
uint32_t sweepStartFreq = 1000;      // 扫频起始频率 1kHz
uint32_t sweepStopFreq = 100000;     // 扫频结束频率 100kHz
uint32_t sweepStep = 1000;           // 扫频步进 1kHz
uint32_t sweepDelay = 10;            // 每个频率停留时间 10ms
uint8_t sweepStarted = 0;            // 扫频开始标志
uint32_t currentSweepFreq = 1000;    // 当前扫频频率
uint8_t sweepDirection = 1;          // 扫频方向 1:向上 0:向下

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_ADC1_Init();
  MX_TIM3_Init();
  MX_USART1_UART_Init();
  MX_TIM4_Init();
  /* USER CODE BEGIN 2 */
   // 初始化AD9959
   ad9959_init();

   // 设置所有通道幅度
   ad9959_set_channel_amp(AD9959_CHANNEL_0, 1023);  // 通道0幅度
   ad9959_set_channel_amp(AD9959_CHANNEL_1, 1023);  // 通道1幅度
   ad9959_set_channel_amp(AD9959_CHANNEL_2, 1023);  // 通道2幅度
   ad9959_set_channel_amp(AD9959_CHANNEL_3, 1023);  // 通道3幅度

   // 通道0输出100Hz正弦波
   ad9959_set_channel_freq(AD9959_CHANNEL_0, 100);

   // 通道1设置为扫频起始频率
   ad9959_set_channel_freq(AD9959_CHANNEL_1, sweepStartFreq);
   currentSweepFreq = sweepStartFreq;

   // 其他通道设置为默认频率
   ad9959_set_channel_freq(AD9959_CHANNEL_2, 7000000);
   ad9959_set_channel_freq(AD9959_CHANNEL_3, 7000000);

   // 设置所有通道相位为0
   ad9959_set_channel_phase(AD9959_CHANNEL_0, 0);
   ad9959_set_channel_phase(AD9959_CHANNEL_1, 0);
   ad9959_set_channel_phase(AD9959_CHANNEL_2, 0);
   ad9959_set_channel_phase(AD9959_CHANNEL_3, 0);

   sweepStarted = 1;

   HAL_TIM_Base_Start(&htim3);              // 启动定时器触发ADC采样
   HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_buff, FFT_LENGTH); // 启动ADC_DMA采集
   printf("AD9959 initialized: CH0=100Hz, CH1=Sweep(1kHz-100kHz)\r\n");

   FFT_InitWindow(window);
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  static uint32_t lastSweepTime = 0;

  while (1)
  {
	  if(flag){
		   // FFT处理和频率检测
		   FFT_Process(adc_buff, testInput, testOutput, window, 1000000.0f);

		   float currentFre = FFT_DetectFundamentalFrequency(testOutput, 1000000.0f, 50.0f, 500000.0f);
		   printf("Detected: %f Hz, CH1 Sweep: %lu Hz\r\n", currentFre, currentSweepFreq);

		   float smoothedFreq = FFT_SmoothFrequency(freqHistory, &freqHistoryIndex, currentFre);

		   float harmonic1 = FFT_DetectHarmonic(testOutput, 1000000.0f, smoothedFreq, 1);
           float harmonic2 = FFT_DetectHarmonic(testOutput, 1000000.0f, smoothedFreq, 2);

           // 重新启动ADC采集
		   HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_buff, FFT_LENGTH);
		   flag = 0;
	  }

	  // 扫频控制逻辑
	  if(sweepStarted && (HAL_GetTick() - lastSweepTime >= sweepDelay)) {
		  lastSweepTime = HAL_GetTick();

		  if(sweepDirection) {
			  // 向上扫频
			  currentSweepFreq += sweepStep;
			  if(currentSweepFreq >= sweepStopFreq) {
				  currentSweepFreq = sweepStopFreq;
				  sweepDirection = 0; // 改变方向，向下扫频
			  }
		  } else {
			  // 向下扫频
			  currentSweepFreq -= sweepStep;
			  if(currentSweepFreq <= sweepStartFreq) {
				  currentSweepFreq = sweepStartFreq;
				  sweepDirection = 1; // 改变方向，向上扫频
			  }
		  }

		  // 更新通道1的频率
		  ad9959_set_channel_freq(AD9959_CHANNEL_1, currentSweepFreq);
	  }

	  HAL_Delay(1); // 短暂延时，避免CPU占用过高

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        flag = 1; // ADC_DMA������ɣ���λ��־
		
		//printf("ADC DMA ������ɣ�\n"); 
    }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
